{"scan_timestamp": "2025-06-01T19:01:06.426181", "found_directories": {"C:\\Program Files\\BlueStacks_nxt": {"size": 478220423, "file_count": 246, "subdirectories": ["C:\\Program Files\\BlueStacks_nxt\\Assets", "C:\\Program Files\\BlueStacks_nxt\\imageformats", "C:\\Program Files\\BlueStacks_nxt\\multimedia", "C:\\Program Files\\BlueStacks_nxt\\platforms", "C:\\Program Files\\BlueStacks_nxt\\Qt", "C:\\Program Files\\BlueStacks_nxt\\Qt5Compat", "C:\\Program Files\\BlueStacks_nxt\\QtMultimedia", "C:\\Program Files\\BlueStacks_nxt\\QtQml", "C:\\Program Files\\BlueStacks_nxt\\QtQuick", "C:\\Program Files\\BlueStacks_nxt\\QtWebChannel", "C:\\Program Files\\BlueStacks_nxt\\QtWebEngine", "C:\\Program Files\\BlueStacks_nxt\\QtWebSockets", "C:\\Program Files\\BlueStacks_nxt\\resources", "C:\\Program Files\\BlueStacks_nxt\\tls", "C:\\Program Files\\BlueStacks_nxt\\translations", "C:\\Program Files\\BlueStacks_nxt\\Qt\\labs", "C:\\Program Files\\BlueStacks_nxt\\Qt\\labs\\platform", "C:\\Program Files\\BlueStacks_nxt\\Qt5Compat\\GraphicalEffects", "C:\\Program Files\\BlueStacks_nxt\\Qt5Compat\\GraphicalEffects\\private", "C:\\Program Files\\BlueStacks_nxt\\QtQml\\WorkerScript", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Dialogs", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Layouts", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Shapes", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Templates", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Window", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\Basic", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\impl", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\Basic\\impl", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Dialogs\\quickimpl", "C:\\Program Files\\BlueStacks_nxt\\translations\\qtwebengine_locales"], "important_files": [], "config_files": [], "executable_files": ["C:\\Program Files\\BlueStacks_nxt\\7zr.exe", "C:\\Program Files\\BlueStacks_nxt\\AdbWinApi.dll", "C:\\Program Files\\BlueStacks_nxt\\agora_rtc_sdk.dll", "C:\\Program Files\\BlueStacks_nxt\\avcodec-60.dll", "C:\\Program Files\\BlueStacks_nxt\\avformat-60.dll", "C:\\Program Files\\BlueStacks_nxt\\avutil-58.dll", "C:\\Program Files\\BlueStacks_nxt\\BlueStacksAppplayerWeb.exe", "C:\\Program Files\\BlueStacks_nxt\\BlueStacksHelper.exe", "C:\\Program Files\\BlueStacks_nxt\\BlueStacksUninstaller.exe", "C:\\Program Files\\BlueStacks_nxt\\boost_json-vc142-mt-x64-1_76.dll", "C:\\Program Files\\BlueStacks_nxt\\brotlicommon.dll", "C:\\Program Files\\BlueStacks_nxt\\brotlidec.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkC.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkDD.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkDD2.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkDDU.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkProxyStub.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkRT.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkSharedFolders.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkSVC.exe", "C:\\Program Files\\BlueStacks_nxt\\BstkTypeLib.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkVMM.dll", "C:\\Program Files\\BlueStacks_nxt\\BstkVMMgr.exe", "C:\\Program Files\\BlueStacks_nxt\\concrt140.dll", "C:\\Program Files\\BlueStacks_nxt\\D3DCompiler_43.dll", "C:\\Program Files\\BlueStacks_nxt\\d3dcompiler_47.dll", "C:\\Program Files\\BlueStacks_nxt\\discord_game_sdk.dll", "C:\\Program Files\\BlueStacks_nxt\\ffmpeg.exe", "C:\\Program Files\\BlueStacks_nxt\\glfw3.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-Aapt.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Adb.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Apn.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-Astcdecoder.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-Bridge-Native.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-CheckCpu.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Common.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-ComRegistrar.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-DataManager.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-DiskCompaction.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-DiskFormatCheck.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-EnableHyperV.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-ForceGPU.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-GLCheck.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Hvutl.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-LogCollector.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-MultiInstanceManager.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Opengl-Native.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-Player.exe", "C:\\Program Files\\BlueStacks_nxt\\HD-Vdes-Service.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-WerHandler.dll", "C:\\Program Files\\BlueStacks_nxt\\HD-WerHandler2.dll", "C:\\Program Files\\BlueStacks_nxt\\libagora-fdkaac.dll", "C:\\Program Files\\BlueStacks_nxt\\libagora-ffmpeg.dll", "C:\\Program Files\\BlueStacks_nxt\\libagora-soundtouch.dll", "C:\\Program Files\\BlueStacks_nxt\\libagora_screen_capture_extension.dll", "C:\\Program Files\\BlueStacks_nxt\\libaosl.dll", "C:\\Program Files\\BlueStacks_nxt\\libcrypto-1_1-x64.dll", "C:\\Program Files\\BlueStacks_nxt\\libEGL.dll", "C:\\Program Files\\BlueStacks_nxt\\libGLESv2.dll", "C:\\Program Files\\BlueStacks_nxt\\libOpenglRender.dll", "C:\\Program Files\\BlueStacks_nxt\\libssl-1_1-x64.dll", "C:\\Program Files\\BlueStacks_nxt\\Microsoft.Win32.TaskScheduler.dll", "C:\\Program Files\\BlueStacks_nxt\\Microsoft.WindowsAPICodePack.dll", "C:\\Program Files\\BlueStacks_nxt\\Microsoft.WindowsAPICodePack.Shell.dll", "C:\\Program Files\\BlueStacks_nxt\\msvcp140.dll", "C:\\Program Files\\BlueStacks_nxt\\msvcp140_1.dll", "C:\\Program Files\\BlueStacks_nxt\\msvcp140_2.dll", "C:\\Program Files\\BlueStacks_nxt\\msvcp140_atomic_wait.dll", "C:\\Program Files\\BlueStacks_nxt\\msvcp140_codecvt_ids.dll", "C:\\Program Files\\BlueStacks_nxt\\Newtonsoft.Json.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Core.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Gui.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Multimedia.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6MultimediaQuick.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Network.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6OpenGL.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Positioning.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Qml.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QmlModels.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QmlWorkerScript.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Quick.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickControls2.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickControls2Basic.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickControls2BasicStyleImpl.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickControls2Impl.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickDialogs2.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickDialogs2QuickImpl.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickDialogs2Utils.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickLayouts.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickShapes.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6QuickTemplates2.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6RemoteObjects.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Svg.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebChannel.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebChannelQuick.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebEngineCore.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebEngineQuick.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebEngineQuickDelegatesQml.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6WebSockets.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Widgets.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt6Xml.dll", "C:\\Program Files\\BlueStacks_nxt\\swresample-4.dll", "C:\\Program Files\\BlueStacks_nxt\\swscale-7.dll", "C:\\Program Files\\BlueStacks_nxt\\VBoxCAPI.dll", "C:\\Program Files\\BlueStacks_nxt\\vccorlib140.dll", "C:\\Program Files\\BlueStacks_nxt\\vcruntime140.dll", "C:\\Program Files\\BlueStacks_nxt\\vcruntime140_1.dll", "C:\\Program Files\\BlueStacks_nxt\\video_dec.dll", "C:\\Program Files\\BlueStacks_nxt\\video_enc.dll", "C:\\Program Files\\BlueStacks_nxt\\zlib1.dll", "C:\\Program Files\\BlueStacks_nxt\\imageformats\\qgif.dll", "C:\\Program Files\\BlueStacks_nxt\\imageformats\\qjpeg.dll", "C:\\Program Files\\BlueStacks_nxt\\imageformats\\qsvg.dll", "C:\\Program Files\\BlueStacks_nxt\\multimedia\\ffmpegmediaplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\multimedia\\windowsmediaplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\platforms\\qwindows.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt\\labs\\platform\\qtlabsplatformplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt5Compat\\GraphicalEffects\\qtgraphicaleffectsplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\Qt5Compat\\GraphicalEffects\\private\\qtgraphicaleffectsprivateplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtMultimedia\\quickmultimediaplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQml\\qmlmetaplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQml\\WorkerScript\\workerscriptplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\qtquick2plugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\qtquickcontrols2plugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\Basic\\qtquickcontrols2basicstyleplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\Basic\\impl\\qtquickcontrols2basicstyleimplplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Controls\\impl\\qtquickcontrols2implplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Dialogs\\qtquickdialogsplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Dialogs\\quickimpl\\qtquickdialogs2quickimplplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Layouts\\qquicklayoutsplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Shapes\\qmlshapesplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Templates\\qtquicktemplates2plugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtQuick\\Window\\quickwindowplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtWebChannel\\webchannelquickplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtWebEngine\\qtwebenginequickplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\QtWebSockets\\qmlwebsocketsplugin.dll", "C:\\Program Files\\BlueStacks_nxt\\tls\\qcertonlybackend.dll", "C:\\Program Files\\BlueStacks_nxt\\tls\\qopensslbackend.dll", "C:\\Program Files\\BlueStacks_nxt\\tls\\qschannelbackend.dll"]}}, "registry_entries": {"SOFTWARE\\BlueStacks_nxt": {"LogDir": "D:\\mondeep\\BlueStacks_nxt\\Logs\\", "DataDir": "D:\\mondeep\\BlueStacks_nxt\\Engine\\", "InstallDir": "C:\\Program Files\\BlueStacks_nxt\\", "UserDefinedDir": "D:\\mondeep\\BlueStacks_nxt", "USER_GUID": "dcfcdb13-e84f-4a1a-8b5a-45fd2e33f9ec", "IsUpgrade": 0, "Version": "5.21.610.1003", "CampaignJson": ""}, "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\BlueStacks_nxt": {"DisplayName": "BlueStacks", "DisplayVersion": "5.21.610.1003", "DisplayIcon": "C:\\Program Files\\BlueStacks_nxt\\ProductLogo.ico", "EstimatedSize": 2096128, "InstallDate": "20241115", "NoModify": 1, "NoRepair": 1, "Publisher": "now.gg, Inc.", "UninstallString": "C:\\Program Files\\BlueStacks_nxt\\BlueStacksUninstaller.exe -tmp"}}, "critical_files": {"BstkSVC.exe": {"path": "C:\\Program Files\\BlueStacks_nxt\\BstkSVC.exe", "description": "BlueStacks service", "size": 4604792}, "HD-Player.exe": {"path": "C:\\Program Files\\BlueStacks_nxt\\HD-Player.exe", "description": "Main BlueStacks executable", "size": 26497392}}, "modification_plan": {"timestamp": "2025-06-01T19:01:06.425182", "target_directories": ["C:\\Program Files\\BlueStacks_nxt"], "critical_files": {"BstkSVC.exe": {"path": "C:\\Program Files\\BlueStacks_nxt\\BstkSVC.exe", "description": "BlueStacks service", "size": 4604792}, "HD-Player.exe": {"path": "C:\\Program Files\\BlueStacks_nxt\\HD-Player.exe", "description": "Main BlueStacks executable", "size": 26497392}}, "modification_steps": [{"step": 2, "action": "Modify Windows Registry", "registry_keys": ["SOFTWARE\\BlueStacks_nxt", "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\BlueStacks_nxt"], "changes": ["Change Guest<PERSON><PERSON> to real device name", "Modify ProductModel to Samsung Galaxy S10", "Set realistic hardware specifications", "Disable emulator detection flags"]}, {"step": 3, "action": "Android System Modifications", "requirements": "Root access in emulator", "changes": ["Replace /system/build.prop with spoofed version", "Remove emulator-specific files", "Install Magisk for advanced hiding", "Configure device fingerprint spoofing"]}]}}