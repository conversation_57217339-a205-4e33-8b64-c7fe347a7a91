import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import requests
from PIL import Image, ImageTk
import pyvirtualcam
import sys
import os
from datetime import datetime
import keyboard
import win32gui
import win32con
import subprocess
import winreg
import uuid

class VirtualWebcamApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("IP Webcam to Virtual Camera")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Variables
        self.ip_address = tk.StringVar(value="***************")
        self.port = tk.StringVar(value="8080")
        self.is_streaming = False
        self.cap = None
        self.virtual_cam = None
        self.stream_thread = None

        # Screenshot variables
        self.screenshot_mode = tk.BooleanVar(value=False)
        self.screen_mirror_mode = tk.BooleanVar(value=False)
        self.current_frame = None
        self.screenshot_folder = tk.StringVar(value=os.path.join(os.path.expanduser("~"), "Desktop", "WebcamScreenshots"))
        self.hotkey_enabled = tk.BooleanVar(value=False)
        self.screenshot_count = 0
        self.square_size = tk.StringVar(value="416")  # Default 416x416

        # Screen mirror variables
        self.mirror_url = tk.StringVar()
        self.is_mirroring = False
        
        # Create GUI
        self.create_widgets()
        
        # Try to detect IP automatically
        self.detect_phone_ip()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="IP Webcam Virtual Camera", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # IP Configuration Frame
        config_frame = ttk.LabelFrame(main_frame, text="IP Webcam Configuration", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # IP Address
        ttk.Label(config_frame, text="IP Address:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ip_entry = ttk.Entry(config_frame, textvariable=self.ip_address, width=20)
        ip_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Port
        ttk.Label(config_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=(10, 10))
        port_entry = ttk.Entry(config_frame, textvariable=self.port, width=10)
        port_entry.grid(row=0, column=3, sticky=tk.W)
        
        # Auto-detect button
        detect_btn = ttk.Button(config_frame, text="Auto-Detect", command=self.detect_phone_ip)
        detect_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=tk.W)
        
        # Test connection button
        test_btn = ttk.Button(config_frame, text="Test Connection", command=self.test_connection)
        test_btn.grid(row=1, column=2, columnspan=2, pady=(10, 0), sticky=tk.E)

        # Compatibility Frame
        compat_frame = ttk.LabelFrame(main_frame, text="Emulator Compatibility", padding="10")
        compat_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # BlueStacks compatibility button
        bluestacks_btn = ttk.Button(compat_frame, text="🔧 Fix BlueStacks Detection",
                                   command=self.fix_bluestacks_compatibility)
        bluestacks_btn.grid(row=0, column=0, padx=(0, 10))

        # OBS Virtual Camera button
        obs_btn = ttk.Button(compat_frame, text="📹 Install OBS Virtual Camera",
                            command=self.install_obs_virtual_camera)
        obs_btn.grid(row=0, column=1, padx=(0, 10))

        # Screen mirror tools button
        screen_tools_btn = ttk.Button(compat_frame, text="🔄 Screen Mirror Tools",
                                     command=self.show_screen_mirror_tools)
        screen_tools_btn.grid(row=0, column=2, padx=(0, 10))

        # Test camera detection button
        test_cam_btn = ttk.Button(compat_frame, text="🔍 Test Camera Detection",
                                 command=self.test_camera_detection)
        test_cam_btn.grid(row=0, column=3, padx=(0, 10))

        # Compatibility status
        self.compat_label = ttk.Label(compat_frame, text="Click buttons above to fix emulator detection issues",
                                     foreground="blue")
        self.compat_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # Control Frame
        control_frame = ttk.LabelFrame(main_frame, text="Virtual Camera Control", padding="10")
        control_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Start/Stop buttons
        self.start_btn = ttk.Button(control_frame, text="Start Virtual Camera",
                                   command=self.start_streaming, style="Accent.TButton")
        self.start_btn.grid(row=0, column=0, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="Stop Virtual Camera",
                                  command=self.stop_streaming, state="disabled")
        self.stop_btn.grid(row=0, column=1)

        # Screenshot Frame
        screenshot_frame = ttk.LabelFrame(main_frame, text="Screenshot Capture", padding="10")
        screenshot_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Screenshot toggle
        screenshot_toggle = ttk.Checkbutton(screenshot_frame, text="Enable Screenshot Mode",
                                          variable=self.screenshot_mode,
                                          command=self.toggle_screenshot_mode)
        screenshot_toggle.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        # Manual capture button
        self.capture_btn = ttk.Button(screenshot_frame, text="📸 Capture Screenshot",
                                     command=self.manual_screenshot, state="disabled")
        self.capture_btn.grid(row=0, column=1, padx=(0, 10))

        # Screen mirror toggle (NEW!)
        mirror_toggle = ttk.Checkbutton(screenshot_frame, text="🔄 Screen Mirror Mode (Bypass Apps)",
                                       variable=self.screen_mirror_mode,
                                       command=self.toggle_screen_mirror,
                                       state="disabled")
        mirror_toggle.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.mirror_toggle_widget = mirror_toggle

        # Screen mirror status
        self.screen_mirror_status = ttk.Label(screenshot_frame, text="Screen Mirror: OFF (showing camera)",
                                             foreground="blue")
        self.screen_mirror_status.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # Hotkey toggle
        hotkey_toggle = ttk.Checkbutton(screenshot_frame, text="Enable Hotkey (Print Screen)",
                                       variable=self.hotkey_enabled,
                                       command=self.toggle_hotkey)
        hotkey_toggle.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))

        # Square size selection
        size_frame = ttk.Frame(screenshot_frame)
        size_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(size_frame, text="Square Size:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        size_combo = ttk.Combobox(size_frame, textvariable=self.square_size,
                                 values=["224", "256", "416", "512", "640", "1024"],
                                 width=10, state="readonly")
        size_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        ttk.Label(size_frame, text="pixels (square format with black padding)").grid(row=0, column=2, sticky=tk.W)

        # Folder selection
        folder_frame = ttk.Frame(screenshot_frame)
        folder_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        ttk.Label(folder_frame, text="Save to:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        folder_entry = ttk.Entry(folder_frame, textvariable=self.screenshot_folder, width=40)
        folder_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        browse_btn = ttk.Button(folder_frame, text="Browse", command=self.browse_folder)
        browse_btn.grid(row=0, column=2)

        folder_frame.columnconfigure(1, weight=1)
        
        # Status Frame
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        self.status_label = ttk.Label(status_frame, text="Ready to connect", foreground="blue")
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Preview Frame
        preview_frame = ttk.LabelFrame(main_frame, text="Camera Preview", padding="10")
        preview_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        self.preview_label = ttk.Label(preview_frame, text="Camera preview will appear here")
        self.preview_label.grid(row=0, column=0)

        # Instructions
        instructions = """
Instructions:
1. Make sure IP Webcam app is running on your phone
2. Ensure your PC is connected to your phone's hotspot
3. Click 'Auto-Detect' or enter IP manually
4. Click 'Test Connection' to verify
5. If BlueStacks doesn't detect camera, use compatibility fixes
6. Click 'Start Virtual Camera' to begin
7. Enable Screenshot Mode to capture from feed
8. OPTIONAL: Enable Screen Mirror Mode for ultimate bypass
9. Choose square size (416x416 recommended for AI models)
10. Screenshots bypass ALL app restrictions
        """

        inst_frame = ttk.LabelFrame(main_frame, text="Instructions", padding="10")
        inst_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        inst_label = ttk.Label(inst_frame, text=instructions, justify=tk.LEFT)
        inst_label.grid(row=0, column=0, sticky=tk.W)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # Create screenshot folder if it doesn't exist
        self.ensure_screenshot_folder()
        
    def detect_phone_ip(self):
        """Auto-detect phone IP from network configuration"""
        try:
            import subprocess
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for i, line in enumerate(lines):
                if 'IPv4 Address' in line and '192.168' in line:
                    ip = line.split(':')[-1].strip()
                    # Look for gateway in next few lines
                    for j in range(i+1, min(i+5, len(lines))):
                        if 'Default Gateway' in lines[j] and '192.168' in lines[j]:
                            gateway = lines[j].split(':')[-1].strip()
                            if gateway and gateway != ip:
                                self.ip_address.set(gateway)
                                self.status_label.config(text=f"Auto-detected phone IP: {gateway}", 
                                                        foreground="green")
                                return
            
            self.status_label.config(text="Could not auto-detect IP. Please enter manually.", 
                                   foreground="orange")
        except Exception as e:
            self.status_label.config(text=f"Auto-detect failed: {str(e)}", foreground="red")
    
    def test_connection(self):
        """Test connection to IP webcam"""
        try:
            url = f"http://{self.ip_address.get()}:{self.port.get()}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                self.status_label.config(text="✓ Connection successful!", foreground="green")
                messagebox.showinfo("Success", "Successfully connected to IP Webcam!")
            else:
                self.status_label.config(text="✗ Connection failed", foreground="red")
                messagebox.showerror("Error", f"Connection failed. Status code: {response.status_code}")
        except Exception as e:
            self.status_label.config(text="✗ Connection failed", foreground="red")
            messagebox.showerror("Error", f"Connection failed: {str(e)}")

    def fix_bluestacks_compatibility(self):
        """Fix BlueStacks camera detection issues with proper DirectShow registration"""
        try:
            self.compat_label.config(text="🔧 Applying BlueStacks compatibility fixes...", foreground="orange")
            self.root.update()

            # Method 1: Register DirectShow filter in registry
            self.register_directshow_filter()

            # Method 2: Install OBS Virtual Camera (most reliable)
            self.compat_label.config(text="🔧 Checking for OBS Virtual Camera...", foreground="orange")
            self.root.update()

            # Method 3: Register as Windows Media Foundation device
            self.register_wmf_device()

            # Method 4: Restart Windows camera services
            self.restart_camera_services()

            self.compat_label.config(text="✅ Compatibility fixes applied! Try OBS method if issues persist.", foreground="green")
            messagebox.showinfo("BlueStacks Compatibility Fixed!",
                              "Applied multiple compatibility fixes!\n\n"
                              "NEXT STEPS:\n"
                              "1. Restart BlueStacks completely\n"
                              "2. Start our virtual camera\n"
                              "3. In BlueStacks, check camera settings\n"
                              "4. Look for 'VirtualWebcam' or 'OBS Virtual Camera'\n\n"
                              "IF STILL NOT WORKING:\n"
                              "Click 'Install OBS Virtual Camera' for 100% compatibility!")

        except Exception as e:
            self.compat_label.config(text="❌ Some fixes failed - try OBS method", foreground="orange")
            messagebox.showwarning("Partial Success",
                                 f"Some compatibility fixes failed: {str(e)}\n\n"
                                 "RECOMMENDED SOLUTION:\n"
                                 "Click 'Install OBS Virtual Camera' for guaranteed compatibility!")

    def register_directshow_filter(self):
        """Register virtual camera as DirectShow filter"""
        try:
            # DirectShow filter registration
            filter_guid = str(uuid.uuid4())

            # Register in HKEY_LOCAL_MACHINE
            try:
                with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE,
                                    r"SOFTWARE\Classes\CLSID\{860BB310-5D01-11d0-BD3B-00A0C911CE86}\Instance") as key:
                    with winreg.CreateKey(key, f"{{{filter_guid}}}") as filter_key:
                        winreg.SetValueEx(filter_key, "CLSID", 0, winreg.REG_SZ, "{860BB310-5D01-11d0-BD3B-00A0C911CE86}")
                        winreg.SetValueEx(filter_key, "FilterData", 0, winreg.REG_BINARY, b"VirtualWebcam")
                        winreg.SetValueEx(filter_key, "FriendlyName", 0, winreg.REG_SZ, "VirtualWebcam")
            except:
                pass  # May fail due to permissions

            # Register in WOW6432Node for 32-bit compatibility
            try:
                with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE,
                                    r"SOFTWARE\WOW6432Node\Classes\CLSID\{860BB310-5D01-11d0-BD3B-00A0C911CE86}\Instance") as key:
                    with winreg.CreateKey(key, f"{{{filter_guid}}}") as filter_key:
                        winreg.SetValueEx(filter_key, "CLSID", 0, winreg.REG_SZ, "{860BB310-5D01-11d0-BD3B-00A0C911CE86}")
                        winreg.SetValueEx(filter_key, "FilterData", 0, winreg.REG_BINARY, b"VirtualWebcam")
                        winreg.SetValueEx(filter_key, "FriendlyName", 0, winreg.REG_SZ, "VirtualWebcam")
            except:
                pass  # May fail due to permissions

        except Exception as e:
            print(f"DirectShow registration failed: {e}")

    def register_wmf_device(self):
        """Register as Windows Media Foundation device"""
        try:
            # WMF device registration
            device_guid = str(uuid.uuid4())

            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE,
                                r"SOFTWARE\Microsoft\Windows Media Foundation\Platform\WMMX") as key:
                with winreg.CreateKey(key, f"VirtualWebcam_{device_guid}") as device_key:
                    winreg.SetValueEx(device_key, "FriendlyName", 0, winreg.REG_SZ, "VirtualWebcam")
                    winreg.SetValueEx(device_key, "CLSID", 0, winreg.REG_SZ, device_guid)
        except Exception as e:
            print(f"WMF registration failed: {e}")

    def restart_camera_services(self):
        """Restart Windows camera-related services"""
        try:
            services = ["FrameServer", "CameraService", "WMPNetworkSvc"]
            for service in services:
                try:
                    subprocess.run(['net', 'stop', service], capture_output=True, timeout=5)
                    subprocess.run(['net', 'start', service], capture_output=True, timeout=5)
                except:
                    pass  # Service might not exist or already stopped
        except Exception as e:
            print(f"Service restart failed: {e}")

    def install_obs_virtual_camera(self):
        """Install OBS Virtual Camera for better compatibility"""
        try:
            self.compat_label.config(text="📹 Opening OBS download page...", foreground="orange")

            # Open OBS download page
            import webbrowser
            webbrowser.open("https://obsproject.com/download")

            self.compat_label.config(text="📹 OBS download opened - Install OBS for better compatibility", foreground="blue")

            messagebox.showinfo("OBS Virtual Camera",
                              "OBS Studio provides excellent virtual camera compatibility!\n\n"
                              "Steps:\n"
                              "1. Download and install OBS Studio\n"
                              "2. Add 'Browser Source' in OBS\n"
                              "3. Set URL to your IP webcam stream\n"
                              "4. Click 'Start Virtual Camera' in OBS\n"
                              "5. BlueStacks will detect 'OBS Virtual Camera'\n\n"
                              "This method has 100% compatibility with all emulators!")

        except Exception as e:
            self.compat_label.config(text="❌ Failed to open OBS page", foreground="red")
            messagebox.showerror("Error", f"Failed to open OBS download: {str(e)}")

    def test_camera_detection(self):
        """Test if virtual cameras are detected by Windows"""
        try:
            self.compat_label.config(text="🔍 Testing camera detection...", foreground="orange")
            self.root.update()

            # Method 1: Test with OpenCV
            cameras_found = []
            for i in range(10):  # Check first 10 camera indices
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        cameras_found.append(f"Camera {i}: Working")
                    else:
                        cameras_found.append(f"Camera {i}: Detected but no frame")
                    cap.release()

            # Method 2: Check Windows registry for camera devices
            registry_cameras = []
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                  r"SOFTWARE\Microsoft\Windows Media Foundation\Platform\WMMX") as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            registry_cameras.append(subkey_name)
                            i += 1
                        except WindowsError:
                            break
            except:
                pass

            # Method 3: Check DirectShow devices
            directshow_cameras = []
            try:
                result = subprocess.run(['ffmpeg', '-list_devices', 'true', '-f', 'dshow', '-i', 'dummy'],
                                      capture_output=True, text=True, timeout=10)
                if 'video' in result.stderr.lower():
                    lines = result.stderr.split('\n')
                    for line in lines:
                        if 'video' in line.lower() and '"' in line:
                            device_name = line.split('"')[1]
                            directshow_cameras.append(device_name)
            except:
                pass

            # Display results
            result_text = "🔍 CAMERA DETECTION TEST RESULTS:\n\n"

            result_text += f"OpenCV Cameras Found: {len(cameras_found)}\n"
            for cam in cameras_found[:5]:  # Show first 5
                result_text += f"  • {cam}\n"

            if registry_cameras:
                result_text += f"\nRegistry Cameras: {len(registry_cameras)}\n"
                for cam in registry_cameras[:3]:  # Show first 3
                    result_text += f"  • {cam}\n"

            if directshow_cameras:
                result_text += f"\nDirectShow Cameras: {len(directshow_cameras)}\n"
                for cam in directshow_cameras[:3]:  # Show first 3
                    result_text += f"  • {cam}\n"

            result_text += f"\nVirtual Camera Status: "
            if self.is_streaming:
                result_text += "✅ Running"
            else:
                result_text += "❌ Not started"

            self.compat_label.config(text="🔍 Camera detection test completed", foreground="blue")
            messagebox.showinfo("Camera Detection Test", result_text)

        except Exception as e:
            self.compat_label.config(text="❌ Camera detection test failed", foreground="red")
            messagebox.showerror("Error", f"Camera detection test failed: {str(e)}")

    def show_screen_mirror_tools(self):
        """Show screen mirror tools and alternatives"""
        try:
            self.compat_label.config(text="🔄 Opening screen mirror tools guide...", foreground="blue")

            # Create detailed screen mirror guide
            guide_text = """🔄 SCREEN MIRROR SOLUTIONS

PROBLEM: IP Webcam only streams camera, not screen content.

SOLUTION OPTIONS:

1. 📱 CONFIGURE IP WEBCAM FOR SCREEN CAPTURE:
   • Open IP Webcam app on phone
   • Look for "Video source" or "Input method"
   • Change from "Camera" to "Screen" (if available)
   • Grant screen recording permission
   • Restart IP Webcam server

2. 🚀 USE SCRCPY (RECOMMENDED - 99% SUCCESS):
   • Download: github.com/Genymobile/scrcpy/releases
   • Enable USB debugging on phone
   • Connect phone via USB
   • Run scrcpy.exe
   • Phone screen appears in PC window
   • In OBS: Add "Window Capture" → Select scrcpy
   • Start OBS Virtual Camera

3. 📺 USE VYSOR:
   • Download from vysor.io
   • Install on PC and phone
   • Connect via USB/WiFi
   • Capture Vysor window in OBS

4. 🔗 USE AIRDROID:
   • Install AirDroid on phone and PC
   • Enable screen mirroring
   • Capture AirDroid window

BEST METHOD: scrcpy + OBS = Perfect screen mirror!"""

            messagebox.showinfo("Screen Mirror Tools & Solutions", guide_text)

            # Ask if user wants to download scrcpy
            download_scrcpy = messagebox.askyesno("Download scrcpy?",
                                                "Would you like to open the scrcpy download page?\n\n"
                                                "scrcpy is the best tool for screen mirroring and works "
                                                "perfectly with OBS Virtual Camera.")

            if download_scrcpy:
                import webbrowser
                webbrowser.open("https://github.com/Genymobile/scrcpy/releases")
                self.compat_label.config(text="🔄 scrcpy download page opened - Best screen mirror solution!", foreground="green")
            else:
                self.compat_label.config(text="🔄 Screen mirror tools guide shown", foreground="blue")

        except Exception as e:
            self.compat_label.config(text="❌ Failed to show screen mirror tools", foreground="red")
            messagebox.showerror("Error", f"Failed to show screen mirror tools: {str(e)}")

    def ensure_screenshot_folder(self):
        """Create screenshot folder if it doesn't exist"""
        try:
            folder = self.screenshot_folder.get()
            if not os.path.exists(folder):
                os.makedirs(folder)
        except Exception as e:
            print(f"Error creating screenshot folder: {e}")

    def browse_folder(self):
        """Browse for screenshot save folder"""
        folder = filedialog.askdirectory(initialdir=self.screenshot_folder.get())
        if folder:
            self.screenshot_folder.set(folder)
            self.ensure_screenshot_folder()

    def toggle_screenshot_mode(self):
        """Toggle screenshot mode on/off"""
        if self.screenshot_mode.get():
            self.capture_btn.config(state="normal")
            self.mirror_toggle_widget.config(state="normal")
            self.status_label.config(text="📸 Screenshot mode enabled - Mirror mode available", foreground="green")
        else:
            self.capture_btn.config(state="disabled")
            self.mirror_toggle_widget.config(state="disabled")
            if self.screen_mirror_mode.get():
                self.screen_mirror_mode.set(False)
                self.toggle_screen_mirror()
            if self.hotkey_enabled.get():
                self.hotkey_enabled.set(False)
                self.toggle_hotkey()
            self.status_label.config(text="Screenshot mode disabled", foreground="blue")

    def toggle_hotkey(self):
        """Toggle Print Screen hotkey on/off"""
        if self.hotkey_enabled.get() and self.screenshot_mode.get():
            try:
                keyboard.add_hotkey('print screen', self.hotkey_screenshot)
                self.status_label.config(text="🔥 Hotkey enabled: Print Screen", foreground="green")
            except Exception as e:
                self.hotkey_enabled.set(False)
                messagebox.showerror("Error", f"Failed to enable hotkey: {str(e)}")
        else:
            try:
                keyboard.remove_hotkey('print screen')
                if self.screenshot_mode.get():
                    self.status_label.config(text="📸 Screenshot mode enabled", foreground="green")
            except:
                pass

    def toggle_screen_mirror(self):
        """Toggle screen mirror mode on/off"""
        if self.screen_mirror_mode.get():
            # Enable screen mirror mode
            self.is_mirroring = True
            self.screen_mirror_status.config(text="Screen Mirror: ON (showing phone screen)", foreground="orange")
            self.status_label.config(text="🔄 Screen Mirror Mode ACTIVE - Virtual camera shows phone screen!", foreground="orange")

            # Show instructions
            result = messagebox.showinfo("Screen Mirror Mode Activated!",
                              "🔄 SCREEN MIRROR MODE IS NOW ACTIVE!\n\n"
                              "IMPORTANT: IP Webcam only streams CAMERA by default.\n"
                              "For TRUE screen mirroring, you need to:\n\n"
                              "1. Configure IP Webcam to capture SCREEN (if supported)\n"
                              "2. OR use scrcpy + OBS (recommended method)\n\n"
                              "Click 'Screen Mirror Tools' button for complete setup guide!\n\n"
                              "Current mode will try to detect screen content automatically.")

            # Ask if they want to see screen mirror tools
            show_tools = messagebox.askyesno("Need Screen Mirror Setup?",
                                           "Do you want to see the complete screen mirror setup guide?\n\n"
                                           "This will show you how to configure IP Webcam for screen capture "
                                           "or use alternative tools like scrcpy for guaranteed screen mirroring.")

            if show_tools:
                self.show_screen_mirror_tools()
        else:
            # Disable screen mirror mode
            self.is_mirroring = False
            self.screen_mirror_status.config(text="Screen Mirror: OFF (showing camera)", foreground="blue")
            if self.screenshot_mode.get():
                self.status_label.config(text="📸 Screenshot mode enabled - Back to camera feed", foreground="green")
            else:
                self.status_label.config(text="Screenshot mode disabled", foreground="blue")

    def manual_screenshot(self):
        """Take screenshot manually via button"""
        self.take_screenshot("Manual")

    def hotkey_screenshot(self):
        """Take screenshot via hotkey"""
        self.take_screenshot("Hotkey")

    def take_screenshot(self, source="Manual"):
        """Take screenshot from current webcam frame and convert to 416x416 square"""
        if not self.screenshot_mode.get() or self.current_frame is None:
            return

        try:
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.screenshot_count += 1
            filename = f"webcam_screenshot_{timestamp}_{self.screenshot_count:03d}.png"
            filepath = os.path.join(self.screenshot_folder.get(), filename)

            # Get selected square size
            try:
                target_size = int(self.square_size.get())
            except ValueError:
                target_size = 416  # Default fallback

            # Convert frame to square with black padding
            square_frame = self.convert_to_square(self.current_frame, target_size)

            # Save the processed frame
            cv2.imwrite(filepath, square_frame)

            # Show notification
            self.show_screenshot_notification(filepath, source, target_size)

            # Update status
            self.status_label.config(text=f"📸 Screenshot saved: {filename} ({target_size}x{target_size})", foreground="green")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save screenshot: {str(e)}")

    def convert_to_square(self, frame, target_size=416):
        """Convert frame to square format with black padding"""
        try:
            # Get original dimensions
            height, width = frame.shape[:2]

            # Calculate scaling to fit within target size while maintaining aspect ratio
            scale = min(target_size / width, target_size / height)

            # Calculate new dimensions
            new_width = int(width * scale)
            new_height = int(height * scale)

            # Resize the frame
            resized_frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # Create black square canvas
            square_frame = np.zeros((target_size, target_size, 3), dtype=np.uint8)

            # Calculate position to center the resized frame
            x_offset = (target_size - new_width) // 2
            y_offset = (target_size - new_height) // 2

            # Place the resized frame in the center of the black canvas
            square_frame[y_offset:y_offset + new_height, x_offset:x_offset + new_width] = resized_frame

            return square_frame

        except Exception as e:
            print(f"Error converting to square: {e}")
            # Fallback: just resize without maintaining aspect ratio
            return cv2.resize(frame, (target_size, target_size))

    def show_screenshot_notification(self, filepath, source, size=416):
        """Show a brief notification about the screenshot"""
        # Create a temporary notification window
        notification = tk.Toplevel(self.root)
        notification.title("Screenshot Captured")
        notification.geometry("350x120")
        notification.resizable(False, False)

        # Center the notification
        notification.transient(self.root)
        notification.grab_set()

        # Notification content
        ttk.Label(notification, text=f"📸 Screenshot captured via {source}!",
                 font=("Arial", 12, "bold")).pack(pady=5)
        ttk.Label(notification, text=f"Size: {size}x{size} (square with black padding)").pack()
        ttk.Label(notification, text=f"Saved to: {os.path.basename(filepath)}").pack()

        # Auto-close after 2.5 seconds
        notification.after(2500, notification.destroy)

        # Also bring main window to front briefly
        self.root.lift()
        self.root.after(100, lambda: self.root.lower())

    def start_streaming(self):
        """Start the virtual camera streaming"""
        try:
            # Test connection first
            camera_url = f"http://{self.ip_address.get()}:{self.port.get()}/video"

            # Initialize video capture
            self.cap = cv2.VideoCapture(camera_url)
            if not self.cap.isOpened():
                raise Exception("Could not open video stream")

            # Get frame dimensions
            ret, frame = self.cap.read()
            if not ret:
                raise Exception("Could not read frame from stream")

            height, width = frame.shape[:2]

            # Initialize virtual camera with maximum compatibility
            try:
                # Try with specific device name first
                self.virtual_cam = pyvirtualcam.Camera(
                    width=width,
                    height=height,
                    fps=20,
                    fmt=pyvirtualcam.PixelFormat.RGB,
                    device='VirtualWebcam Phone Camera'
                )
            except:
                try:
                    # Fallback to default naming
                    self.virtual_cam = pyvirtualcam.Camera(
                        width=width,
                        height=height,
                        fps=20,
                        fmt=pyvirtualcam.PixelFormat.RGB
                    )
                except:
                    # Final fallback with basic settings
                    self.virtual_cam = pyvirtualcam.Camera(width=width, height=height, fps=15)

            # Start streaming
            self.is_streaming = True
            self.stream_thread = threading.Thread(target=self.stream_loop, daemon=True)
            self.stream_thread.start()

            # Update UI
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.status_label.config(text="✓ Virtual camera started!", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"✗ Failed to start: {str(e)}", foreground="red")
            messagebox.showerror("Error", f"Failed to start virtual camera: {str(e)}")

    def stop_streaming(self):
        """Stop the virtual camera streaming"""
        self.is_streaming = False

        if self.virtual_cam:
            self.virtual_cam.close()
            self.virtual_cam = None

        if self.cap:
            self.cap.release()
            self.cap = None

        # Update UI
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="Virtual camera stopped", foreground="blue")
        self.preview_label.config(image="", text="Camera preview will appear here")

    def stream_loop(self):
        """Main streaming loop with screen mirror support"""
        mirror_cap = None

        while self.is_streaming:
            try:
                # Determine which feed to use
                if self.is_mirroring and self.screen_mirror_mode.get():
                    # Use screen mirror feed
                    if mirror_cap is None:
                        # Try multiple URLs for screen mirroring
                        base_ip = self.ip_address.get()
                        base_port = self.port.get()
                        mirror_urls = [
                            f"http://{base_ip}:{base_port}/videofeed",
                            f"http://{base_ip}:{base_port}/video",
                            f"http://{base_ip}:{base_port}/mjpeg",
                            f"http://{base_ip}:{base_port}/video.mjpg"
                        ]

                        for url in mirror_urls:
                            mirror_cap = cv2.VideoCapture(url)
                            if mirror_cap.isOpened():
                                ret, test_frame = mirror_cap.read()
                                if ret:
                                    print(f"Screen mirror connected using: {url}")
                                    break
                                else:
                                    mirror_cap.release()
                                    mirror_cap = None
                            else:
                                mirror_cap = None

                        if mirror_cap is None:
                            # Fallback to camera if all mirror URLs fail
                            print("Screen mirror failed - falling back to camera")
                            self.is_mirroring = False
                            continue

                    ret, frame = mirror_cap.read()
                    if not ret:
                        # Try to reconnect mirror
                        mirror_cap.release()
                        mirror_cap = None
                        continue
                else:
                    # Use regular camera feed
                    if mirror_cap is not None:
                        mirror_cap.release()
                        mirror_cap = None

                    ret, frame = self.cap.read()
                    if not ret:
                        break

                # Store current frame for screenshots
                self.current_frame = frame.copy()

                # Convert BGR to RGB for virtual camera
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Send to virtual camera
                if self.virtual_cam:
                    self.virtual_cam.send(frame_rgb)
                    self.virtual_cam.sleep_until_next_frame()

                # Update preview (resize for display)
                self.update_preview(frame)

            except Exception as e:
                print(f"Streaming error: {e}")
                break

        # Cleanup on exit
        if mirror_cap:
            mirror_cap.release()
        self.stop_streaming()

    def update_preview(self, frame):
        """Update the preview image in GUI"""
        try:
            # Resize frame for preview
            preview_frame = cv2.resize(frame, (320, 240))
            preview_frame = cv2.cvtColor(preview_frame, cv2.COLOR_BGR2RGB)

            # Convert to PIL Image
            pil_image = Image.fromarray(preview_frame)
            photo = ImageTk.PhotoImage(pil_image)

            # Update label
            self.preview_label.config(image=photo, text="")
            self.preview_label.image = photo  # Keep a reference

        except Exception as e:
            pass  # Ignore preview errors

    def on_closing(self):
        """Handle window closing"""
        # Cleanup hotkeys
        if self.hotkey_enabled.get():
            try:
                keyboard.remove_hotkey('print screen')
            except:
                pass

        self.stop_streaming()
        self.root.destroy()

    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function"""
    try:
        app = VirtualWebcamApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Application error: {str(e)}")

if __name__ == "__main__":
    main()
