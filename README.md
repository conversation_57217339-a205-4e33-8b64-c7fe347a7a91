# Virtual Webcam - IP Camera to Virtual Camera

Transform your phone's IP webcam into a virtual camera device that works with any Windows application!

## 🎯 What This Does

This application captures your phone's IP webcam stream and creates a virtual camera device in Windows. Once running, your phone camera will appear as "Virtual Camera" in:

- **Video Calling Apps:** Zoom, <PERSON>, Discord, Skype, Google Meet
- **Streaming Software:** OBS Studio, XSplit, Streamlabs
- **Recording Apps:** Any app that uses camera input
- **Web Browsers:** Chrome, Firefox, Edge for web-based video calls

## 📸 NEW: Screenshot Bypass Feature

**Revolutionary screenshot capture that bypasses app restrictions!**

- ✅ **Captures directly from webcam feed** - bypasses black screen protection
- ✅ **Works with ANY app** - even apps that block screenshots
- ✅ **Print Screen hotkey** - seamless integration
- ✅ **Manual capture button** - click to capture anytime
- ✅ **High quality images** - full resolution from phone camera
- ✅ **Auto-saves with timestamps** - organized file naming

## 📱 Prerequisites

1. **IP Webcam app** installed on your Android phone
2. **Phone connected to PC** via hotspot or same WiFi network
3. **Windows PC** with the VirtualWebcam.exe

## 🚀 Quick Start

### Step 1: Setup Phone
1. Install "IP Webcam" from Google Play Store
2. Open the app and configure settings:
   - Resolution: 720p or 1080p
   - Quality: 70-90%
   - FPS: 20-30
3. Scroll down and tap "Start Server"
4. Note the IP address shown (e.g., ************:8080)

### Step 2: Run Virtual Camera
1. **Double-click VirtualWebcam.exe**
2. **Click "Auto-Detect"** (or enter IP manually)
3. **Click "Test Connection"** to verify
4. **Click "Start Virtual Camera"**
5. **Success!** Your phone is now a virtual camera

### Step 3: Use in Other Apps
1. Open any video calling app (Zoom, Teams, etc.)
2. Go to camera settings
3. Select **"Virtual Camera"** instead of built-in camera
4. Enjoy HD video from your phone!

### Step 4: Enable Screenshot Mode (NEW!)
1. **Check "Enable Screenshot Mode"** in the app
2. **Optional:** Enable "Print Screen" hotkey
3. **Choose save folder** or use default (Desktop/WebcamScreenshots)
4. **Take screenshots:**
   - Click "📸 Capture Screenshot" button, OR
   - Press **Print Screen** key (if hotkey enabled)
5. **Screenshots bypass all app restrictions!**

## 🔧 Features

### Virtual Camera Features:
- ✅ **Auto-detection** of phone IP address
- ✅ **Live preview** of camera feed
- ✅ **High quality** video streaming
- ✅ **Low latency** for real-time use
- ✅ **Easy setup** with GUI interface
- ✅ **Works with any app** that uses camera
- ✅ **No data usage** (local network only)

### Screenshot Bypass Features:
- ✅ **Bypasses app restrictions** - works even with protected apps
- ✅ **Print Screen hotkey** - seamless integration
- ✅ **Manual capture button** - click to capture
- ✅ **Auto-timestamped filenames** - organized saving
- ✅ **Custom save location** - choose your folder
- ✅ **Instant notifications** - know when captured
- ✅ **Full resolution** - captures at webcam quality

## 📊 System Requirements

- **Windows 10/11**
- **Python 3.7+** (for source code)
- **4GB RAM** minimum
- **Network connection** to phone

## 🛠️ Troubleshooting

### "Connection Failed"
- Ensure IP Webcam is running on phone
- Check if PC and phone are on same network
- Try manual IP entry instead of auto-detect
- Disable Windows Firewall temporarily

### "Virtual Camera Not Appearing"
- Restart the application
- Try running as Administrator
- Restart the video calling app
- Check Windows Camera Privacy settings

### "Poor Video Quality"
- Increase resolution in IP Webcam app
- Ensure good lighting on phone
- Use USB connection instead of WiFi
- Close other network-intensive apps

## 📁 Files Included

- `VirtualWebcam.exe` - Main executable (71MB)
- `virtual_webcam.py` - Source code
- `requirements.txt` - Python dependencies
- `setup.py` - Build script
- `README.md` - This file

## 🔄 Updates

To update the application:
1. Download new version
2. Replace VirtualWebcam.exe
3. Run normally

## 💡 Tips

- **Position phone at eye level** for natural video calls
- **Use good lighting** for better video quality
- **Keep phone plugged in** to prevent battery drain
- **Use landscape mode** for wider field of view
- **Close unnecessary apps** on phone for better performance

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure all prerequisites are met
3. Try restarting both phone and PC
4. Check network connectivity

---

**Enjoy your new virtual webcam! 📹✨**
