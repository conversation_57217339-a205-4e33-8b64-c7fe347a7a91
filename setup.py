import sys
import subprocess
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def install_pyinstaller():
    """Install PyInstaller for creating executable"""
    print("Installing PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install PyInstaller: {e}")
        return False

def create_executable():
    """Create executable using PyInstaller"""
    print("Creating executable...")
    try:
        cmd = [
            "pyinstaller",
            "--onefile",
            "--windowed",
            "--name=VirtualWebcam",
            "--icon=NONE",
            "virtual_webcam.py"
        ]
        subprocess.check_call(cmd)
        print("✓ Executable created successfully!")
        print("✓ Find your executable in the 'dist' folder: VirtualWebcam.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to create executable: {e}")
        return False

def main():
    print("=== Virtual Webcam Setup ===")
    print("This script will:")
    print("1. Install required Python packages")
    print("2. Install PyInstaller")
    print("3. Create a standalone executable")
    print()
    
    # Check if Python is available
    print(f"Using Python: {sys.executable}")
    print(f"Python version: {sys.version}")
    print()
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please check the error messages above.")
        return
    
    # Install PyInstaller
    if not install_pyinstaller():
        print("Setup failed. Please check the error messages above.")
        return
    
    # Create executable
    if not create_executable():
        print("Setup failed. Please check the error messages above.")
        return
    
    print()
    print("=== Setup Complete! ===")
    print("Your VirtualWebcam.exe is ready in the 'dist' folder.")
    print()
    print("Usage:")
    print("1. Run VirtualWebcam.exe")
    print("2. Make sure IP Webcam is running on your phone")
    print("3. Click 'Auto-Detect' or enter your phone's IP")
    print("4. Click 'Start Virtual Camera'")
    print("5. Your phone camera will appear as 'Virtual Camera' in other apps")

if __name__ == "__main__":
    main()
