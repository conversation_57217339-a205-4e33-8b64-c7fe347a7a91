@echo off
REM Netflix Security Research - BlueStacks Backup Script
REM Created: 2025-06-01 19:01:06

echo Creating backup of BlueStacks configuration...
set BACKUP_DIR=C:\Netflix_Security_Backup\BlueStacks_%date:~-4,4%%date:~-10,2%%date:~-7,2%

mkdir "%BACKUP_DIR%"

REM Backup registry
echo Backing up registry...
reg export "HKEY_LOCAL_MACHINE\SOFTWARE\BlueStacks_nxt" "%BACKUP_DIR%\bluestacks_registry.reg"

REM Backup configuration files
copy "C:\Program Files\BlueStacks_nxt\BstkSVC.exe" "%BACKUP_DIR%\BstkSVC.exe.backup"
copy "C:\Program Files\BlueStacks_nxt\HD-Player.exe" "%BACKUP_DIR%\HD-Player.exe.backup"

echo Backup completed successfully!
echo Backup location: %BACKUP_DIR%
pause
