@echo off
echo ========================================
echo Virtual Webcam Builder
echo ========================================
echo.

echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo.
echo Starting setup process...
python setup.py

echo.
echo Build process completed!
echo Check the 'dist' folder for VirtualWebcam.exe
echo.
pause
