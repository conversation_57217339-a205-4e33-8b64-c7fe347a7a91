@echo off
echo ========================================
echo    WIRELESS SCRCPY SETUP
echo ========================================
echo.

echo Step 1: Enable phone hotspot and connect PC to it
echo Step 2: Connect phone via USB first (one time setup)
echo.
pause

echo Setting up TCP/IP mode...
adb tcpip 5555
echo.

echo Disconnect USB cable now and press any key...
pause

echo.
echo Trying common hotspot IP addresses...
echo.

echo Trying ************:5555...
adb connect ************:5555
if %errorlevel% == 0 (
    echo SUCCESS! Connected wirelessly
    echo Starting scrcpy...
    scrcpy.exe
    goto end
)

echo Trying **************:5555...
adb connect **************:5555
if %errorlevel% == 0 (
    echo SUCCESS! Connected wirelessly
    echo Starting scrcpy...
    scrcpy.exe
    goto end
)

echo Trying ***********:5555...
adb connect ***********:5555
if %errorlevel% == 0 (
    echo SUCCESS! Connected wirelessly
    echo Starting scrcpy...
    scrcpy.exe
    goto end
)

echo.
echo Could not connect automatically.
echo Please check your phone's IP address in hotspot settings
echo and run: adb connect [IP_ADDRESS]:5555
echo.

:end
pause
