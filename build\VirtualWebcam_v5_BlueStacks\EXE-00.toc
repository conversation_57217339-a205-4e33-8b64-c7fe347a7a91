('C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\dist\\VirtualWebcam_v5_BlueStacks.exe',
 False,
 False,
 False,
 'E:\\New '
 'folder\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON>alse,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\VirtualWebcam_v5_BlueStacks.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'E:\\New '
   'folder\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'E:\\New '
   'folder\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('virtual_webcam',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\virtual_webcam.py',
   'PYSOURCE'),
  ('python311.dll', 'E:\\New folder\\python311.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'E:\\New folder\\Lib\\site-packages\\pywin32_system32\\pywintypes311.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom311.dll',
   'E:\\New folder\\Lib\\site-packages\\pywin32_system32\\pythoncom311.dll',
   'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'E:\\New folder\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('_decimal.pyd', 'E:\\New folder\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\New folder\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\New folder\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\New folder\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\New folder\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'E:\\New folder\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\New folder\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\New folder\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'E:\\New folder\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\New folder\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\New folder\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\New folder\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\markupsafe\\_speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'E:\\New folder\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\New folder\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'E:\\New folder\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('pyvirtualcam\\_native_windows_unity_capture.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\pyvirtualcam\\_native_windows_unity_capture.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pyvirtualcam\\_native_windows_obs.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\pyvirtualcam\\_native_windows_obs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'E:\\New folder\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'E:\\New folder\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'E:\\New folder\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'E:\\New folder\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'E:\\New folder\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'E:\\New folder\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\New folder\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'E:\\New folder\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'E:\\New '
   'folder\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'E:\\New folder\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'E:\\New folder\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'E:\\New folder\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll', 'E:\\New folder\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'E:\\New folder\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\New folder\\DLLs\\libffi-8.dll', 'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'E:\\New folder\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\MSVCP140.dll',
   'BINARY'),
  ('tk86t.dll', 'E:\\New folder\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'E:\\New folder\\DLLs\\tcl86t.dll', 'BINARY'),
  ('python3.dll', 'E:\\New folder\\python3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('certifi\\py.typed',
   'E:\\New folder\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\New folder\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tk_data\\tk.tcl', 'E:\\New folder\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tk_data\\images\\README',
   'E:\\New folder\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'E:\\New folder\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\entry.tcl', 'E:\\New folder\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'E:\\New folder\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'E:\\New folder\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'E:\\New folder\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GB', 'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'E:\\New folder\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'E:\\New folder\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'E:\\New folder\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tk_data\\menu.tcl', 'E:\\New folder\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tk_data\\scale.tcl', 'E:\\New folder\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tclIndex', 'E:\\New folder\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'E:\\New folder\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tk_data\\button.tcl', 'E:\\New folder\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'E:\\New folder\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'E:\\New folder\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'E:\\New folder\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'E:\\New folder\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\icons.tcl', 'E:\\New folder\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'E:\\New folder\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\init.tcl', 'E:\\New folder\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'E:\\New folder\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'E:\\New folder\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'E:\\New folder\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\text.tcl', 'E:\\New folder\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'E:\\New folder\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'E:\\New folder\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tk_data\\console.tcl', 'E:\\New folder\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tk_data\\palette.tcl', 'E:\\New folder\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'E:\\New folder\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'E:\\New folder\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'E:\\New folder\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'E:\\New folder\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tk_data\\focus.tcl', 'E:\\New folder\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\word.tcl', 'E:\\New folder\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'E:\\New folder\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'E:\\New folder\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\tclIndex', 'E:\\New folder\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'E:\\New folder\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'E:\\New folder\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'E:\\New folder\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'E:\\New folder\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'E:\\New folder\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'E:\\New folder\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'E:\\New folder\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'E:\\New folder\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'E:\\New folder\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'E:\\New folder\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'E:\\New folder\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'E:\\New folder\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'E:\\New folder\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'E:\\New folder\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'E:\\New folder\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'E:\\New folder\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'E:\\New folder\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'E:\\New folder\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config-3.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\config.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'E:\\New folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'E:\\New '
   'folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'E:\\New folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'E:\\New '
   'folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'E:\\New folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'E:\\New '
   'folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'E:\\New '
   'folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'E:\\New folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'E:\\New folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'E:\\New folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'E:\\New folder\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'E:\\New folder\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('cv2\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'E:\\New folder\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\OneDrive\\Desktop\\c,c++,html,js,css\\build\\VirtualWebcam_v5_BlueStacks\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1748759414,
 [('runw.exe',
   'E:\\New '
   'folder\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'E:\\New folder\\python311.dll')
