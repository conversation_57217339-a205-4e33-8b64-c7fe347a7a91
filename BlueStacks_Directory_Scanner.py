#!/usr/bin/env python3
"""
Netflix Security Research - BlueStacks 5 Directory Scanner
Comprehensive tool to locate and analyze BlueStacks installation for security modifications
"""

import os
import sys
import subprocess
import winreg
from pathlib import Path
import json
from datetime import datetime

class BlueStacksScanner:
    def __init__(self):
        self.found_directories = {}
        self.registry_entries = {}
        self.config_files = {}
        self.executable_files = {}
        
    def scan_common_locations(self):
        """Scan common BlueStacks installation locations"""
        print("🔍 Scanning common BlueStacks 5 locations...")
        
        common_paths = [
            r"C:\Program Files\BlueStacks_nxt",
            r"C:\Program Files (x86)\BlueStacks_nxt", 
            r"C:\ProgramData\BlueStacks_nxt",
            r"C:\Users\<USER>\AppData\Local\BlueStacks_nxt".format(os.getenv('USERNAME')),
            r"C:\Users\<USER>\AppData\Roaming\BlueStacks_nxt".format(os.getenv('USERNAME')),
            r"C:\BlueStacks_nxt",
            r"D:\BlueStacks_nxt",
            r"E:\BlueStacks_nxt"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                self.found_directories[path] = self.analyze_directory(path)
                print(f"✅ Found: {path}")
            else:
                print(f"❌ Not found: {path}")
                
    def search_entire_c_drive(self):
        """Search entire C: drive for BlueStacks directories"""
        print("🔍 Searching entire C: drive for BlueStacks...")
        
        search_patterns = ["BlueStacks", "bluestacks", "nxt"]
        
        for root, dirs, files in os.walk("C:\\"):
            try:
                for dir_name in dirs:
                    for pattern in search_patterns:
                        if pattern.lower() in dir_name.lower():
                            full_path = os.path.join(root, dir_name)
                            if "BlueStacks" in dir_name:
                                self.found_directories[full_path] = self.analyze_directory(full_path)
                                print(f"✅ Found directory: {full_path}")
            except (PermissionError, OSError):
                continue
                
    def analyze_directory(self, directory):
        """Analyze BlueStacks directory structure"""
        analysis = {
            "size": 0,
            "file_count": 0,
            "subdirectories": [],
            "important_files": [],
            "config_files": [],
            "executable_files": []
        }
        
        try:
            for root, dirs, files in os.walk(directory):
                analysis["subdirectories"].extend([os.path.join(root, d) for d in dirs])
                
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        file_size = os.path.getsize(file_path)
                        analysis["size"] += file_size
                        analysis["file_count"] += 1
                        
                        # Categorize important files
                        if file.endswith(('.exe', '.dll')):
                            analysis["executable_files"].append(file_path)
                        elif file.endswith(('.conf', '.cfg', '.ini', '.prop', '.xml', '.json')):
                            analysis["config_files"].append(file_path)
                        elif file in ['bluestacks.conf', 'UserData.spb', 'build.prop']:
                            analysis["important_files"].append(file_path)
                            
                    except (PermissionError, OSError):
                        continue
                        
        except (PermissionError, OSError):
            pass
            
        return analysis
    
    def scan_registry(self):
        """Scan Windows registry for BlueStacks entries"""
        print("🔍 Scanning Windows registry for BlueStacks...")
        
        registry_paths = [
            r"SOFTWARE\BlueStacks_nxt",
            r"SOFTWARE\WOW6432Node\BlueStacks_nxt",
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\BlueStacks_nxt"
        ]
        
        for reg_path in registry_paths:
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                self.registry_entries[reg_path] = self.read_registry_key(key)
                winreg.CloseKey(key)
                print(f"✅ Found registry: {reg_path}")
            except FileNotFoundError:
                print(f"❌ Registry not found: {reg_path}")
            except Exception as e:
                print(f"❌ Registry error: {reg_path} - {e}")
                
    def read_registry_key(self, key):
        """Read all values from a registry key"""
        values = {}
        try:
            i = 0
            while True:
                try:
                    name, value, type = winreg.EnumValue(key, i)
                    values[name] = value
                    i += 1
                except OSError:
                    break
        except Exception:
            pass
        return values
    
    def find_critical_files(self):
        """Find critical BlueStacks files for modification"""
        print("🔍 Locating critical files for security modifications...")
        
        critical_files = {
            "bluestacks.conf": "Main configuration file",
            "UserData.spb": "User data and settings",
            "build.prop": "Android system properties",
            "HD-Player.exe": "Main BlueStacks executable",
            "HD-Agent.exe": "BlueStacks agent process",
            "BstkSVC.exe": "BlueStacks service",
            "HD-Frontend.exe": "BlueStacks frontend"
        }
        
        found_critical = {}
        
        for directory in self.found_directories:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file in critical_files:
                        file_path = os.path.join(root, file)
                        found_critical[file] = {
                            "path": file_path,
                            "description": critical_files[file],
                            "size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
                        }
                        
        self.config_files = found_critical
        return found_critical
    
    def generate_modification_plan(self):
        """Generate detailed modification plan for security research"""
        print("📋 Generating modification plan...")
        
        plan = {
            "timestamp": datetime.now().isoformat(),
            "target_directories": list(self.found_directories.keys()),
            "critical_files": self.config_files,
            "modification_steps": []
        }
        
        # Configuration file modifications
        if "bluestacks.conf" in self.config_files:
            plan["modification_steps"].append({
                "step": 1,
                "action": "Modify bluestacks.conf",
                "file": self.config_files["bluestacks.conf"]["path"],
                "changes": [
                    "Change instance name from 'BlueStacks' to 'Samsung Galaxy S10'",
                    "Modify CPU cores to realistic count (8)",
                    "Set memory to realistic amount (8192MB)",
                    "Disable root access detection",
                    "Change graphics mode to hardware acceleration"
                ]
            })
            
        # Registry modifications
        if self.registry_entries:
            plan["modification_steps"].append({
                "step": 2,
                "action": "Modify Windows Registry",
                "registry_keys": list(self.registry_entries.keys()),
                "changes": [
                    "Change GuestName to real device name",
                    "Modify ProductModel to Samsung Galaxy S10",
                    "Set realistic hardware specifications",
                    "Disable emulator detection flags"
                ]
            })
            
        # Android system modifications
        plan["modification_steps"].append({
            "step": 3,
            "action": "Android System Modifications",
            "requirements": "Root access in emulator",
            "changes": [
                "Replace /system/build.prop with spoofed version",
                "Remove emulator-specific files",
                "Install Magisk for advanced hiding",
                "Configure device fingerprint spoofing"
            ]
        })
        
        return plan
    
    def create_backup_script(self):
        """Create backup script for original files"""
        backup_script = f"""@echo off
REM Netflix Security Research - BlueStacks Backup Script
REM Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

echo Creating backup of BlueStacks configuration...
set BACKUP_DIR=C:\\Netflix_Security_Backup\\BlueStacks_%date:~-4,4%%date:~-10,2%%date:~-7,2%

mkdir "%BACKUP_DIR%"

REM Backup registry
echo Backing up registry...
reg export "HKEY_LOCAL_MACHINE\\SOFTWARE\\BlueStacks_nxt" "%BACKUP_DIR%\\bluestacks_registry.reg"

REM Backup configuration files
"""
        
        for file_name, file_info in self.config_files.items():
            backup_script += f'copy "{file_info["path"]}" "%BACKUP_DIR%\\{file_name}.backup"\n'
            
        backup_script += """
echo Backup completed successfully!
echo Backup location: %BACKUP_DIR%
pause
"""
        
        with open("BlueStacks_Backup.bat", "w") as f:
            f.write(backup_script)
            
        print("✅ Backup script created: BlueStacks_Backup.bat")
    
    def run_comprehensive_scan(self):
        """Execute complete BlueStacks scanning and analysis"""
        print("🔒 Netflix Security Research - BlueStacks 5 Scanner")
        print("=" * 60)
        
        # Scan for BlueStacks installations
        self.scan_common_locations()
        
        if not self.found_directories:
            print("⚠️  No BlueStacks found in common locations. Searching entire C: drive...")
            self.search_entire_c_drive()
            
        # Scan registry
        self.scan_registry()
        
        # Find critical files
        self.find_critical_files()
        
        # Generate modification plan
        plan = self.generate_modification_plan()
        
        # Create backup script
        self.create_backup_script()
        
        # Save detailed report
        report = {
            "scan_timestamp": datetime.now().isoformat(),
            "found_directories": self.found_directories,
            "registry_entries": self.registry_entries,
            "critical_files": self.config_files,
            "modification_plan": plan
        }
        
        with open("BlueStacks_Analysis_Report.json", "w") as f:
            json.dump(report, f, indent=2)
            
        # Display summary
        print("\n🎯 SCAN RESULTS SUMMARY")
        print("=" * 30)
        print(f"Directories found: {len(self.found_directories)}")
        print(f"Registry entries: {len(self.registry_entries)}")
        print(f"Critical files: {len(self.config_files)}")
        
        if self.found_directories:
            print("\n📁 FOUND DIRECTORIES:")
            for directory in self.found_directories:
                print(f"  • {directory}")
                
        if self.config_files:
            print("\n📄 CRITICAL FILES FOR MODIFICATION:")
            for file_name, file_info in self.config_files.items():
                print(f"  • {file_name}: {file_info['path']}")
                
        print(f"\n📊 Detailed report saved: BlueStacks_Analysis_Report.json")
        print("🔧 Backup script created: BlueStacks_Backup.bat")
        
        return report

def main():
    print("Netflix Security Research - BlueStacks 5 Directory Scanner")
    print("This tool locates BlueStacks installations for security modification")
    print("\nScanning may take several minutes for complete C: drive search...")
    
    scanner = BlueStacksScanner()
    scanner.run_comprehensive_scan()

if __name__ == "__main__":
    main()
