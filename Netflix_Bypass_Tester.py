#!/usr/bin/env python3
"""
Netflix Security Research - BlueStacks Bypass Effectiveness Tester
Quick validation tool for emulator detection bypass
"""

import subprocess
import time
import os
import json
from datetime import datetime

class BypassTester:
    def __init__(self):
        self.test_results = {}
        self.adb_connected = False
        
    def check_adb_connection(self):
        """Check if ADB is connected to BlueStacks"""
        print("🔍 Checking ADB connection...")
        try:
            result = subprocess.run(["adb", "devices"], capture_output=True, text=True)
            if "5555" in result.stdout and "device" in result.stdout:
                print("✅ ADB connected to BlueStacks")
                self.adb_connected = True
                return True
            else:
                print("❌ ADB not connected. Attempting connection...")
                subprocess.run(["adb", "connect", "localhost:5555"], capture_output=True)
                time.sleep(2)
                return self.check_adb_connection()
        except Exception as e:
            print(f"❌ ADB connection failed: {e}")
            return False
    
    def test_device_spoofing(self):
        """Test if device properties are properly spoofed"""
        print("\n🎭 Testing Device Spoofing...")
        
        if not self.adb_connected:
            print("❌ ADB not connected")
            return False
            
        tests = {
            "ro.product.model": "SM-G973F",
            "ro.product.manufacturer": "samsung",
            "ro.product.brand": "samsung",
            "ro.hardware": "qcom",
            "ro.kernel.qemu": "0",
            "ro.boot.qemu": "0"
        }
        
        results = {}
        for prop, expected in tests.items():
            try:
                result = subprocess.run(
                    ["adb", "shell", "getprop", prop], 
                    capture_output=True, text=True
                )
                actual = result.stdout.strip()
                success = actual == expected
                results[prop] = {
                    "expected": expected,
                    "actual": actual,
                    "status": "PASS" if success else "FAIL"
                }
                status_icon = "✅" if success else "❌"
                print(f"  {status_icon} {prop}: {actual} (expected: {expected})")
            except Exception as e:
                results[prop] = {"error": str(e), "status": "ERROR"}
                print(f"  ❌ {prop}: Error - {e}")
                
        self.test_results["device_spoofing"] = results
        return all(r["status"] == "PASS" for r in results.values())
    
    def test_emulator_file_hiding(self):
        """Test if emulator files are properly hidden"""
        print("\n📁 Testing Emulator File Hiding...")
        
        emulator_files = [
            "/system/bin/qemu-props",
            "/system/lib/libc_malloc_debug_qemu.so",
            "/system/lib/libqemu_pipe.so",
            "/dev/socket/qemud",
            "/dev/qemu_pipe"
        ]
        
        results = {}
        for file_path in emulator_files:
            try:
                result = subprocess.run(
                    ["adb", "shell", "test", "-e", file_path, "&&", "echo", "EXISTS", "||", "echo", "HIDDEN"],
                    capture_output=True, text=True
                )
                status = result.stdout.strip()
                success = status == "HIDDEN"
                results[file_path] = {
                    "status": "PASS" if success else "FAIL",
                    "detected": status == "EXISTS"
                }
                status_icon = "✅" if success else "❌"
                print(f"  {status_icon} {file_path}: {status}")
            except Exception as e:
                results[file_path] = {"error": str(e), "status": "ERROR"}
                print(f"  ❌ {file_path}: Error - {e}")
                
        self.test_results["emulator_files"] = results
        return all(r["status"] == "PASS" for r in results.values())
    
    def test_netflix_installation(self):
        """Test Netflix app installation and basic functionality"""
        print("\n🎬 Testing Netflix Installation...")
        
        try:
            # Check if Netflix is installed
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages", "com.netflix.mediaclient"],
                capture_output=True, text=True
            )
            
            if "com.netflix.mediaclient" in result.stdout:
                print("✅ Netflix is installed")
                
                # Try to launch Netflix
                launch_result = subprocess.run(
                    ["adb", "shell", "am", "start", "-n", "com.netflix.mediaclient/.ui.launch.UIWebViewActivity"],
                    capture_output=True, text=True
                )
                
                if launch_result.returncode == 0:
                    print("✅ Netflix launched successfully")
                    self.test_results["netflix_installation"] = {"status": "PASS", "installed": True, "launchable": True}
                    return True
                else:
                    print("❌ Netflix failed to launch")
                    self.test_results["netflix_installation"] = {"status": "FAIL", "installed": True, "launchable": False}
                    return False
            else:
                print("❌ Netflix not installed")
                print("📋 Install Netflix from Play Store or use: adb install netflix.apk")
                self.test_results["netflix_installation"] = {"status": "FAIL", "installed": False}
                return False
                
        except Exception as e:
            print(f"❌ Netflix test failed: {e}")
            self.test_results["netflix_installation"] = {"error": str(e), "status": "ERROR"}
            return False
    
    def test_banking_app_compatibility(self):
        """Test banking app compatibility (generic test)"""
        print("\n🏦 Testing Banking App Compatibility...")
        
        # Check for common banking apps
        banking_packages = [
            "com.chase.sig.android",
            "com.bankofamerica.digitalwallet",
            "com.wellsfargo.mobile.android",
            "com.usaa.mobile.android.usaa"
        ]
        
        installed_banking_apps = []
        for package in banking_packages:
            try:
                result = subprocess.run(
                    ["adb", "shell", "pm", "list", "packages", package],
                    capture_output=True, text=True
                )
                if package in result.stdout:
                    installed_banking_apps.append(package)
                    print(f"✅ Found banking app: {package}")
            except:
                continue
                
        if installed_banking_apps:
            print(f"✅ {len(installed_banking_apps)} banking app(s) detected")
            self.test_results["banking_apps"] = {"status": "PASS", "apps": installed_banking_apps}
            return True
        else:
            print("ℹ️  No banking apps installed for testing")
            self.test_results["banking_apps"] = {"status": "SKIP", "apps": []}
            return True
    
    def test_system_integrity(self):
        """Test overall system integrity after modifications"""
        print("\n🔧 Testing System Integrity...")
        
        try:
            # Test basic Android functionality
            result = subprocess.run(
                ["adb", "shell", "pm", "list", "packages"],
                capture_output=True, text=True
            )
            
            if result.returncode == 0 and len(result.stdout) > 100:
                print("✅ Package manager working")
                
                # Test settings access
                settings_result = subprocess.run(
                    ["adb", "shell", "am", "start", "-a", "android.settings.SETTINGS"],
                    capture_output=True, text=True
                )
                
                if settings_result.returncode == 0:
                    print("✅ Settings accessible")
                    self.test_results["system_integrity"] = {"status": "PASS"}
                    return True
                else:
                    print("❌ Settings not accessible")
                    self.test_results["system_integrity"] = {"status": "FAIL", "issue": "settings"}
                    return False
            else:
                print("❌ Package manager issues")
                self.test_results["system_integrity"] = {"status": "FAIL", "issue": "package_manager"}
                return False
                
        except Exception as e:
            print(f"❌ System integrity test failed: {e}")
            self.test_results["system_integrity"] = {"error": str(e), "status": "ERROR"}
            return False
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n📊 Generating Test Report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results,
            "summary": {
                "total_tests": len(self.test_results),
                "passed": sum(1 for r in self.test_results.values() if r.get("status") == "PASS"),
                "failed": sum(1 for r in self.test_results.values() if r.get("status") == "FAIL"),
                "errors": sum(1 for r in self.test_results.values() if r.get("status") == "ERROR"),
                "skipped": sum(1 for r in self.test_results.values() if r.get("status") == "SKIP")
            }
        }
        
        # Calculate success rate
        total_meaningful = report["summary"]["passed"] + report["summary"]["failed"]
        if total_meaningful > 0:
            success_rate = (report["summary"]["passed"] / total_meaningful) * 100
            report["summary"]["success_rate"] = f"{success_rate:.1f}%"
        else:
            report["summary"]["success_rate"] = "0%"
            
        # Save report
        report_filename = f"netflix_bypass_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"✅ Test report saved: {report_filename}")
        return report
    
    def run_comprehensive_test(self):
        """Run all bypass effectiveness tests"""
        print("🔒 Netflix Security Research - Bypass Effectiveness Testing")
        print("=" * 60)
        
        # Check prerequisites
        if not self.check_adb_connection():
            print("❌ Cannot proceed without ADB connection")
            return False
            
        # Run all tests
        tests = [
            ("Device Spoofing", self.test_device_spoofing),
            ("Emulator File Hiding", self.test_emulator_file_hiding),
            ("Netflix Installation", self.test_netflix_installation),
            ("Banking App Compatibility", self.test_banking_app_compatibility),
            ("System Integrity", self.test_system_integrity)
        ]
        
        passed_tests = 0
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"❌ {test_name} failed with error: {e}")
                
        # Generate report
        report = self.generate_test_report()
        
        # Display summary
        print("\n🎯 TEST SUMMARY")
        print("=" * 30)
        print(f"Tests Passed: {report['summary']['passed']}")
        print(f"Tests Failed: {report['summary']['failed']}")
        print(f"Success Rate: {report['summary']['success_rate']}")
        
        if report['summary']['passed'] >= 3:
            print("\n🎉 BYPASS EFFECTIVENESS: HIGH")
            print("✅ Ready for Netflix and banking app testing")
        elif report['summary']['passed'] >= 2:
            print("\n⚠️  BYPASS EFFECTIVENESS: MEDIUM")
            print("🔧 Some improvements needed")
        else:
            print("\n❌ BYPASS EFFECTIVENESS: LOW")
            print("🔧 Significant improvements required")
            
        return passed_tests >= 3

def main():
    print("Netflix Security Research - BlueStacks Bypass Tester")
    print("This tool validates the effectiveness of our emulator detection bypass")
    print("\nEnsure BlueStacks is running before proceeding...")
    
    input("Press Enter to start testing...")
    
    tester = BypassTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🚀 READY FOR NETFLIX TESTING!")
        print("Next steps:")
        print("1. Install Netflix from Play Store")
        print("2. Sign in and test video playback")
        print("3. Test banking apps for root detection")
        print("4. Document any detection mechanisms found")
    else:
        print("\n🔧 BYPASS NEEDS IMPROVEMENT")
        print("Review the test results and apply additional modifications")

if __name__ == "__main__":
    main()
