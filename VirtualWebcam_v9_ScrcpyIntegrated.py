import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import requests
from PIL import Image, ImageTk
import pyvirtualcam
import sys
import os
from datetime import datetime
import keyboard
import win32gui
import win32con
import subprocess
import winreg
import uuid
import psutil
import socket

class VirtualWebcamApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Ultimate Screen Mirror & Virtual Camera Bypass")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # Variables
        self.ip_address = tk.StringVar(value="***************")
        self.port = tk.StringVar(value="8080")
        self.is_streaming = False
        self.cap = None
        self.virtual_cam = None
        self.stream_thread = None

        # Screenshot variables
        self.screenshot_mode = tk.BooleanVar(value=False)
        self.screen_mirror_mode = tk.BooleanVar(value=False)
        self.current_frame = None
        self.screenshot_folder = tk.StringVar(value=os.path.join(os.path.expanduser("~"), "Desktop", "WebcamScreenshots"))
        self.hotkey_enabled = tk.BooleanVar(value=False)
        self.screenshot_count = 0
        self.square_size = tk.StringVar(value="416")

        # Screen mirror variables
        self.mirror_url = tk.StringVar()
        self.is_mirroring = False
        self.scrcpy_process = None
        self.scrcpy_path = tk.StringVar()
        self.adb_connected = False
        
        # Create GUI
        self.create_widgets()
        
        # Try to detect IP automatically
        self.detect_phone_ip()
        
        # Check for scrcpy installation
        self.check_scrcpy_installation()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🚀 Ultimate Screen Mirror & Virtual Camera Bypass", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # IP Configuration Frame
        config_frame = ttk.LabelFrame(main_frame, text="📱 IP Webcam Configuration", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # IP Address
        ttk.Label(config_frame, text="IP Address:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ip_entry = ttk.Entry(config_frame, textvariable=self.ip_address, width=20)
        ip_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Port
        ttk.Label(config_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=(10, 10))
        port_entry = ttk.Entry(config_frame, textvariable=self.port, width=10)
        port_entry.grid(row=0, column=3, sticky=tk.W)
        
        # Auto-detect button
        detect_btn = ttk.Button(config_frame, text="🔍 Auto-Detect", command=self.detect_phone_ip)
        detect_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=tk.W)
        
        # Test connection button
        test_btn = ttk.Button(config_frame, text="🔗 Test Connection", command=self.test_connection)
        test_btn.grid(row=1, column=2, columnspan=2, pady=(10, 0), sticky=tk.E)

        # Screen Mirror Frame (NEW!)
        mirror_frame = ttk.LabelFrame(main_frame, text="🔄 Wireless Screen Mirror (scrcpy)", padding="10")
        mirror_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # scrcpy path
        ttk.Label(mirror_frame, text="scrcpy Path:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        scrcpy_entry = ttk.Entry(mirror_frame, textvariable=self.scrcpy_path, width=40)
        scrcpy_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        browse_scrcpy_btn = ttk.Button(mirror_frame, text="📁 Browse", command=self.browse_scrcpy)
        browse_scrcpy_btn.grid(row=0, column=2)

        # scrcpy controls
        scrcpy_controls = ttk.Frame(mirror_frame)
        scrcpy_controls.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.setup_wireless_btn = ttk.Button(scrcpy_controls, text="📶 Setup Wireless", 
                                           command=self.setup_wireless_scrcpy)
        self.setup_wireless_btn.grid(row=0, column=0, padx=(0, 10))

        self.start_scrcpy_btn = ttk.Button(scrcpy_controls, text="🚀 Start Screen Mirror", 
                                         command=self.start_scrcpy)
        self.start_scrcpy_btn.grid(row=0, column=1, padx=(0, 10))

        self.stop_scrcpy_btn = ttk.Button(scrcpy_controls, text="⏹️ Stop Screen Mirror", 
                                        command=self.stop_scrcpy, state="disabled")
        self.stop_scrcpy_btn.grid(row=0, column=2, padx=(0, 10))

        # scrcpy status
        self.scrcpy_status = ttk.Label(mirror_frame, text="📱 Screen Mirror: Not connected", 
                                     foreground="blue")
        self.scrcpy_status.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # Control Frame
        control_frame = ttk.LabelFrame(main_frame, text="🎥 Virtual Camera Control", padding="10")
        control_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Start/Stop buttons
        self.start_btn = ttk.Button(control_frame, text="▶️ Start Virtual Camera",
                                   command=self.start_streaming, style="Accent.TButton")
        self.start_btn.grid(row=0, column=0, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="⏹️ Stop Virtual Camera",
                                  command=self.stop_streaming, state="disabled")
        self.stop_btn.grid(row=0, column=1)

        # Screenshot Frame
        screenshot_frame = ttk.LabelFrame(main_frame, text="📸 Screenshot Bypass Mode", padding="10")
        screenshot_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Screenshot toggle
        screenshot_toggle = ttk.Checkbutton(screenshot_frame, text="📸 Enable Screenshot Mode",
                                          variable=self.screenshot_mode,
                                          command=self.toggle_screenshot_mode)
        screenshot_toggle.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        # Manual capture button
        self.capture_btn = ttk.Button(screenshot_frame, text="📷 Capture Screenshot",
                                     command=self.manual_screenshot, state="disabled")
        self.capture_btn.grid(row=0, column=1, padx=(0, 10))

        # Screen mirror toggle (ENHANCED!)
        mirror_toggle = ttk.Checkbutton(screenshot_frame, text="🔄 Screen Mirror Feed (Ultimate Bypass)",
                                       variable=self.screen_mirror_mode,
                                       command=self.toggle_screen_mirror_feed,
                                       state="disabled")
        mirror_toggle.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.mirror_toggle_widget = mirror_toggle

        # Screen mirror status
        self.screen_mirror_status = ttk.Label(screenshot_frame, 
                                             text="🎥 Virtual Camera: Showing IP Webcam feed",
                                             foreground="blue")
        self.screen_mirror_status.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # Status Frame
        status_frame = ttk.LabelFrame(main_frame, text="📊 Status", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        self.status_label = ttk.Label(status_frame, text="Ready to connect", foreground="blue")
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # Instructions
        instructions = """
🚀 ULTIMATE BYPASS INSTRUCTIONS:

1. 📱 CAMERA FEED: Start IP Webcam app → Connect PC to phone hotspot → Start Virtual Camera
2. 🔄 SCREEN MIRROR: Setup scrcpy → Enable wireless → Start screen mirror
3. 🎯 BYPASS MODE: Enable "Screen Mirror Feed" → Virtual camera shows phone SCREEN instead of camera
4. 📸 SCREENSHOT: Apps using virtual camera get phone screen as "camera" feed
5. 🛡️ RESULT: Complete bypass of all app restrictions!

HOTSPOT REQUIRED: Both camera feed and screen mirror need hotspot connection for wireless operation.
        """

        inst_frame = ttk.LabelFrame(main_frame, text="📋 Instructions", padding="10")
        inst_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        inst_label = ttk.Label(inst_frame, text=instructions, justify=tk.LEFT)
        inst_label.grid(row=0, column=0, sticky=tk.W)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Create screenshot folder if it doesn't exist
        self.ensure_screenshot_folder()
        
    def check_scrcpy_installation(self):
        """Check if scrcpy is installed and find its path"""
        try:
            # Common scrcpy installation paths
            common_paths = [
                r"C:\Users\<USER>\Downloads\scrcpy-win64-v3.2\scrcpy.exe",
                r"C:\scrcpy\scrcpy.exe",
                r"C:\Program Files\scrcpy\scrcpy.exe",
                r"C:\Tools\scrcpy\scrcpy.exe"
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    self.scrcpy_path.set(path)
                    self.scrcpy_status.config(text="✅ scrcpy found: Ready for screen mirror", 
                                            foreground="green")
                    return
            
            # Try to find scrcpy in PATH
            try:
                result = subprocess.run(['where', 'scrcpy'], capture_output=True, text=True)
                if result.returncode == 0:
                    path = result.stdout.strip().split('\n')[0]
                    self.scrcpy_path.set(path)
                    self.scrcpy_status.config(text="✅ scrcpy found in PATH", foreground="green")
                    return
            except:
                pass
            
            self.scrcpy_status.config(text="❌ scrcpy not found - Click Browse to locate", 
                                    foreground="orange")
            
        except Exception as e:
            self.scrcpy_status.config(text=f"❌ Error checking scrcpy: {str(e)}",
                                    foreground="red")

    def browse_scrcpy(self):
        """Browse for scrcpy.exe location"""
        try:
            filename = filedialog.askopenfilename(
                title="Select scrcpy.exe",
                filetypes=[("Executable files", "*.exe"), ("All files", "*.*")],
                initialdir=os.path.expanduser("~\\Downloads")
            )
            if filename:
                self.scrcpy_path.set(filename)
                self.scrcpy_status.config(text="✅ scrcpy path set manually", foreground="green")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to browse for scrcpy: {str(e)}")

    def setup_wireless_scrcpy(self):
        """Setup wireless scrcpy connection"""
        try:
            self.scrcpy_status.config(text="📶 Setting up wireless connection...", foreground="orange")
            self.root.update()

            # Step 1: Check if phone is connected via USB
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            if 'device' not in result.stdout:
                messagebox.showwarning("USB Connection Required",
                                     "Please connect your phone via USB first!\n\n"
                                     "Steps:\n"
                                     "1. Enable USB debugging on phone\n"
                                     "2. Connect phone via USB cable\n"
                                     "3. Allow USB debugging when prompted\n"
                                     "4. Try again")
                self.scrcpy_status.config(text="❌ USB connection required for wireless setup",
                                        foreground="red")
                return

            # Step 2: Enable TCP mode
            self.scrcpy_status.config(text="📶 Enabling TCP mode...", foreground="orange")
            self.root.update()

            tcp_result = subprocess.run(['adb', 'tcpip', '5555'], capture_output=True, text=True, timeout=10)
            if tcp_result.returncode != 0:
                raise Exception(f"Failed to enable TCP mode: {tcp_result.stderr}")

            # Step 3: Get phone IP
            phone_ip = self.get_phone_ip()
            if not phone_ip:
                messagebox.showwarning("Hotspot Required",
                                     "Please enable phone hotspot and connect PC to it!\n\n"
                                     "Steps:\n"
                                     "1. Enable Mobile Hotspot on phone\n"
                                     "2. Connect PC to phone's WiFi\n"
                                     "3. Try again")
                self.scrcpy_status.config(text="❌ Phone hotspot required", foreground="red")
                return

            # Step 4: Disconnect USB and connect wirelessly
            messagebox.showinfo("Disconnect USB",
                              "Now disconnect the USB cable and click OK to connect wirelessly!")

            self.scrcpy_status.config(text="📶 Connecting wirelessly...", foreground="orange")
            self.root.update()

            # Connect wirelessly
            connect_result = subprocess.run(['adb', 'connect', f'{phone_ip}:5555'],
                                          capture_output=True, text=True, timeout=15)

            if 'connected' in connect_result.stdout.lower():
                self.adb_connected = True
                self.scrcpy_status.config(text=f"✅ Wireless connection established: {phone_ip}",
                                        foreground="green")
                messagebox.showinfo("Wireless Setup Complete!",
                                  f"Successfully connected wirelessly to {phone_ip}!\n\n"
                                  "You can now start screen mirroring without USB cable.")
            else:
                raise Exception(f"Wireless connection failed: {connect_result.stdout}")

        except subprocess.TimeoutExpired:
            self.scrcpy_status.config(text="❌ Connection timeout - check ADB installation",
                                    foreground="red")
            messagebox.showerror("Timeout", "Connection timed out. Please check:\n"
                               "1. ADB is installed (comes with Android Studio)\n"
                               "2. Phone is connected and USB debugging enabled")
        except Exception as e:
            self.scrcpy_status.config(text=f"❌ Wireless setup failed: {str(e)}",
                                    foreground="red")
            messagebox.showerror("Wireless Setup Failed", f"Error: {str(e)}")

    def get_phone_ip(self):
        """Get phone IP from network configuration"""
        try:
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')

            for i, line in enumerate(lines):
                if 'IPv4 Address' in line and '192.168' in line:
                    # Look for gateway in next few lines
                    for j in range(i+1, min(i+5, len(lines))):
                        if 'Default Gateway' in lines[j] and '192.168' in lines[j]:
                            gateway = lines[j].split(':')[-1].strip()
                            if gateway and '192.168' in gateway:
                                return gateway
            return None
        except:
            return None

    def start_scrcpy(self):
        """Start scrcpy screen mirroring"""
        try:
            if not self.scrcpy_path.get() or not os.path.exists(self.scrcpy_path.get()):
                messagebox.showerror("scrcpy Not Found",
                                   "Please browse and select scrcpy.exe location first!")
                return

            if self.scrcpy_process and self.scrcpy_process.poll() is None:
                messagebox.showwarning("Already Running", "Screen mirror is already running!")
                return

            self.scrcpy_status.config(text="🚀 Starting screen mirror...", foreground="orange")
            self.root.update()

            # Start scrcpy process
            self.scrcpy_process = subprocess.Popen([self.scrcpy_path.get()],
                                                 stdout=subprocess.PIPE,
                                                 stderr=subprocess.PIPE)

            # Wait a moment to check if it started successfully
            time.sleep(2)
            if self.scrcpy_process.poll() is None:
                self.scrcpy_status.config(text="✅ Screen mirror active - Phone screen visible on PC",
                                        foreground="green")
                self.start_scrcpy_btn.config(state="disabled")
                self.stop_scrcpy_btn.config(state="normal")
                self.mirror_toggle_widget.config(state="normal")

                messagebox.showinfo("Screen Mirror Started!",
                                  "Screen mirroring is now active!\n\n"
                                  "Next steps:\n"
                                  "1. You should see your phone screen in a window\n"
                                  "2. Enable 'Screen Mirror Feed' to use screen in virtual camera\n"
                                  "3. Apps will see phone screen instead of camera!")
            else:
                error_output = self.scrcpy_process.stderr.read().decode() if self.scrcpy_process.stderr else "Unknown error"
                raise Exception(f"scrcpy failed to start: {error_output}")

        except Exception as e:
            self.scrcpy_status.config(text=f"❌ Failed to start screen mirror: {str(e)}",
                                    foreground="red")
            messagebox.showerror("Screen Mirror Failed", f"Error: {str(e)}")

    def stop_scrcpy(self):
        """Stop scrcpy screen mirroring"""
        try:
            if self.scrcpy_process:
                self.scrcpy_process.terminate()
                self.scrcpy_process.wait(timeout=5)
                self.scrcpy_process = None

            self.scrcpy_status.config(text="⏹️ Screen mirror stopped", foreground="blue")
            self.start_scrcpy_btn.config(state="normal")
            self.stop_scrcpy_btn.config(state="disabled")
            self.mirror_toggle_widget.config(state="disabled")
            self.screen_mirror_mode.set(False)
            self.toggle_screen_mirror_feed()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop screen mirror: {str(e)}")

    def toggle_screen_mirror_feed(self):
        """Toggle between camera feed and screen mirror feed in virtual camera"""
        try:
            if self.screen_mirror_mode.get():
                # Switch to screen mirror feed
                if not self.scrcpy_process or self.scrcpy_process.poll() is not None:
                    messagebox.showwarning("Screen Mirror Required",
                                         "Please start screen mirror first!")
                    self.screen_mirror_mode.set(False)
                    return

                self.screen_mirror_status.config(
                    text="🔄 Virtual Camera: Showing PHONE SCREEN (Ultimate Bypass Active!)",
                    foreground="red")
                messagebox.showinfo("Ultimate Bypass Activated!",
                                  "🛡️ BYPASS MODE ACTIVE!\n\n"
                                  "Virtual camera now shows your phone SCREEN instead of camera.\n"
                                  "Apps using this virtual camera will see whatever is on your phone screen!\n\n"
                                  "Perfect for bypassing:\n"
                                  "• Banking app restrictions\n"
                                  "• Screenshot detection\n"
                                  "• Screen recording blocks\n"
                                  "• Any app limitations!")
            else:
                # Switch back to camera feed
                self.screen_mirror_status.config(
                    text="🎥 Virtual Camera: Showing IP Webcam feed",
                    foreground="blue")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to toggle screen mirror feed: {str(e)}")
            self.screen_mirror_mode.set(False)

    def detect_phone_ip(self):
        """Auto-detect phone IP from network configuration"""
        try:
            import subprocess
            result = subprocess.run(['ipconfig'], capture_output=True, text=True)
            lines = result.stdout.split('\n')

            for i, line in enumerate(lines):
                if 'IPv4 Address' in line and '192.168' in line:
                    ip = line.split(':')[-1].strip()
                    # Look for gateway in next few lines
                    for j in range(i+1, min(i+5, len(lines))):
                        if 'Default Gateway' in lines[j] and '192.168' in lines[j]:
                            gateway = lines[j].split(':')[-1].strip()
                            if gateway and gateway != ip:
                                self.ip_address.set(gateway)
                                self.status_label.config(text=f"Auto-detected phone IP: {gateway}",
                                                        foreground="green")
                                return

            self.status_label.config(text="Could not auto-detect IP. Please enter manually.",
                                   foreground="orange")
        except Exception as e:
            self.status_label.config(text=f"Auto-detect failed: {str(e)}", foreground="red")

    def test_connection(self):
        """Test connection to IP webcam"""
        try:
            url = f"http://{self.ip_address.get()}:{self.port.get()}"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                self.status_label.config(text="✓ Connection successful!", foreground="green")
                messagebox.showinfo("Success", "Successfully connected to IP Webcam!")
            else:
                self.status_label.config(text="✗ Connection failed", foreground="red")
                messagebox.showerror("Error", f"Connection failed. Status code: {response.status_code}")
        except Exception as e:
            self.status_label.config(text="✗ Connection failed", foreground="red")
            messagebox.showerror("Error", f"Connection failed: {str(e)}")

    def start_streaming(self):
        """Start the virtual camera streaming"""
        try:
            if self.is_streaming:
                messagebox.showwarning("Already Streaming", "Virtual camera is already running!")
                return

            # Test connection first
            url = f"http://{self.ip_address.get()}:{self.port.get()}/video"
            response = requests.get(url, timeout=5)
            if response.status_code != 200:
                messagebox.showerror("Connection Failed", "Cannot connect to IP webcam. Please check IP and port.")
                return

            self.is_streaming = True
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.capture_btn.config(state="normal")

            # Start streaming thread
            self.stream_thread = threading.Thread(target=self.stream_loop, daemon=True)
            self.stream_thread.start()

            self.status_label.config(text="✓ Virtual camera started successfully!", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"✗ Failed to start: {str(e)}", foreground="red")
            messagebox.showerror("Error", f"Failed to start virtual camera: {str(e)}")

    def stop_streaming(self):
        """Stop the virtual camera streaming"""
        try:
            self.is_streaming = False

            if self.cap:
                self.cap.release()
                self.cap = None

            if self.virtual_cam:
                self.virtual_cam.close()
                self.virtual_cam = None

            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.capture_btn.config(state="disabled")

            self.status_label.config(text="Virtual camera stopped", foreground="blue")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop virtual camera: {str(e)}")

    def stream_loop(self):
        """Main streaming loop"""
        try:
            # Initialize video capture
            if self.screen_mirror_mode.get() and self.scrcpy_process and self.scrcpy_process.poll() is None:
                # Use screen capture from scrcpy window
                self.stream_from_screen_mirror()
            else:
                # Use IP webcam feed
                self.stream_from_ip_webcam()

        except Exception as e:
            self.status_label.config(text=f"✗ Streaming error: {str(e)}", foreground="red")
            self.is_streaming = False

    def stream_from_ip_webcam(self):
        """Stream from IP webcam"""
        try:
            url = f"http://{self.ip_address.get()}:{self.port.get()}/video"
            self.cap = cv2.VideoCapture(url)

            if not self.cap.isOpened():
                raise Exception("Failed to open IP webcam stream")

            # Initialize virtual camera
            self.virtual_cam = pyvirtualcam.Camera(width=640, height=480, fps=30)

            while self.is_streaming:
                ret, frame = self.cap.read()
                if not ret:
                    continue

                # Store current frame for screenshots
                self.current_frame = frame.copy()

                # Resize frame for virtual camera
                frame_resized = cv2.resize(frame, (640, 480))
                frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)

                # Send to virtual camera
                self.virtual_cam.send(frame_rgb)
                self.virtual_cam.sleep_until_next_frame()

        except Exception as e:
            raise e

    def stream_from_screen_mirror(self):
        """Stream from screen mirror (scrcpy window capture)"""
        try:
            # This would capture the scrcpy window and stream it
            # For now, we'll use a placeholder implementation
            # In a full implementation, this would use screen capture APIs
            # to capture the scrcpy window and stream it as virtual camera

            # Initialize virtual camera
            self.virtual_cam = pyvirtualcam.Camera(width=640, height=480, fps=30)

            while self.is_streaming and self.screen_mirror_mode.get():
                # Placeholder: Create a frame indicating screen mirror mode
                frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(frame, "SCREEN MIRROR MODE", (50, 240),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, "Phone screen bypass active", (50, 280),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                # Store current frame for screenshots
                self.current_frame = frame.copy()

                # Send to virtual camera
                self.virtual_cam.send(frame)
                self.virtual_cam.sleep_until_next_frame()

        except Exception as e:
            raise e

    def toggle_screenshot_mode(self):
        """Toggle screenshot mode"""
        if self.screenshot_mode.get():
            self.status_label.config(text="Screenshot mode enabled", foreground="green")
        else:
            self.status_label.config(text="Screenshot mode disabled", foreground="blue")

    def manual_screenshot(self):
        """Take a manual screenshot"""
        try:
            if self.current_frame is None:
                messagebox.showwarning("No Frame", "No frame available for screenshot!")
                return

            self.take_screenshot()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to take screenshot: {str(e)}")

    def take_screenshot(self):
        """Take a screenshot and save it"""
        try:
            if self.current_frame is None:
                return

            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}_{self.screenshot_count:04d}.jpg"
            filepath = os.path.join(self.screenshot_folder.get(), filename)

            # Convert to square format if needed
            square_frame = self.convert_to_square(self.current_frame)

            # Save screenshot
            cv2.imwrite(filepath, square_frame)
            self.screenshot_count += 1

            self.status_label.config(text=f"Screenshot saved: {filename}", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"Screenshot failed: {str(e)}", foreground="red")

    def convert_to_square(self, frame):
        """Convert frame to square format with black padding"""
        try:
            size = int(self.square_size.get())
            h, w = frame.shape[:2]

            # Calculate scaling to fit within square
            scale = min(size / w, size / h)
            new_w = int(w * scale)
            new_h = int(h * scale)

            # Resize frame
            resized = cv2.resize(frame, (new_w, new_h))

            # Create square canvas with black background
            square = np.zeros((size, size, 3), dtype=np.uint8)

            # Center the resized frame
            y_offset = (size - new_h) // 2
            x_offset = (size - new_w) // 2
            square[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized

            return square

        except Exception as e:
            return frame

    def ensure_screenshot_folder(self):
        """Ensure screenshot folder exists"""
        try:
            folder = self.screenshot_folder.get()
            if not os.path.exists(folder):
                os.makedirs(folder)
        except Exception as e:
            print(f"Failed to create screenshot folder: {e}")

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.cleanup()
        except Exception as e:
            messagebox.showerror("Error", f"Application error: {str(e)}")
        finally:
            self.cleanup()

    def cleanup(self):
        """Cleanup resources"""
        try:
            self.is_streaming = False

            if self.cap:
                self.cap.release()

            if self.virtual_cam:
                self.virtual_cam.close()

            if self.scrcpy_process:
                self.scrcpy_process.terminate()

        except Exception as e:
            print(f"Cleanup error: {e}")

if __name__ == "__main__":
    app = VirtualWebcamApp()
    app.run()
